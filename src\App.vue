<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
/* Global styles for the application */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Microsoft YaHei', sans-serif;
  background-color: #0F2E2C;
  color: #fff;
  overflow: hidden;
  /* 背景图片设置 (Background image settings) */
  background-image: url('./bg.png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

#app {
  width: 100vw;
  height: 100vh;
  position: relative;
  z-index: 1;
  /* 确保应用内容在背景图片之上 (Ensure app content is above background image) */
  background-color: transparent;
  /* 透明以显示背景图片 (Transparent to show background image) */
}

/* Global scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 204, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 204, 0.7);
}

/* Smooth fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

#app {
  animation: fadeIn 1s ease-out;
}
</style>