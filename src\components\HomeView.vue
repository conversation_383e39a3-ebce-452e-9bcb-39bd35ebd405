<template>
  <div class="yunnan-laos-home">
    <!-- 样式刷新控制面板 (Style refresh control panel) -->
    <div class="style-controls" v-if="showStyleControls">
      <button @click="refreshCurrentStyle" :disabled="isRefreshing" class="control-btn">
        {{ isRefreshing ? '刷新中...' : '刷新样式' }}
      </button>
      <button @click="forceRefreshStyle" :disabled="isRefreshing" class="control-btn">
        强制刷新缓存
      </button>
      <button @click="reinitializeMap" :disabled="isRefreshing" class="control-btn">
        重新初始化
      </button>
      <button @click="toggleStyleControls" class="control-btn toggle-btn">
        隐藏控制
      </button>
    </div>

    <!-- 显示控制按钮 (Show controls button) -->
    <div class="show-controls" v-if="!showStyleControls">
      <button @click="toggleStyleControls" class="control-btn show-btn">
        样式控制
      </button>
    </div>

    <!-- 地图容器 (Map Container) -->
    <standalone-world-map ref="worldMap" :path-data="pathData" :height="'100vh'" :animation-enabled="true"
      :flow-animation-speed="0.008" :flow-particle-count="3" :regional-mode="true" :region-config="regionConfig"
      :show-title="false" :minimalist-mode="true" :show-grid="false" @map-ready="onMapReady" />
  </div>
</template>

<script>
import StandaloneWorldMap from './StandaloneWorldMap.vue'
import { yunnanLaosRoutes, yunnanLaosMapConfig } from '@/data/yunnanLaosRoutes.js'

export default {
  name: 'HomeView',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      // 地图数据 (Map Data)
      pathData: [],

      // 区域配置 (Regional Configuration)
      regionConfig: {
        name: 'yunnan-laos',
        center: yunnanLaosMapConfig.center,
        zoom: yunnanLaosMapConfig.zoom,
        bounds: yunnanLaosMapConfig.bounds
      },

      // 样式控制相关 (Style control related)
      showStyleControls: false, // 是否显示样式控制面板 (Whether to show style control panel)
      isRefreshing: false // 是否正在刷新样式 (Whether style is refreshing)
    }
  },
  methods: {
    /**
     * 加载云南-老挝路径数据 (Load Yunnan-Laos route data)
     */
    loadYunnanLaosData() {
      console.log('加载云南-老挝路径数据 (Loading Yunnan-Laos route data)...')

      // 转换数据格式为组件所需格式 (Convert data format for component)
      this.pathData = yunnanLaosRoutes.map(route => ({
        id: route.id,
        coords: route.coords,
        value: route.value,
        name: route.name,
        type: route.type,
        metadata: route.metadata
      }))

      console.log(`云南-老挝数据加载完成 (Yunnan-Laos data loaded): ${this.pathData.length} 条路径 (routes)`)
    },





    /**
     * 地图就绪回调 (Map ready callback)
     */
    onMapReady(map) {
      console.log('云南-老挝区域地图就绪 (Yunnan-Laos regional map ready):', map)
      console.log('地图中心点 (Map center):', map.getCenter())
      console.log('地图缩放级别 (Map zoom level):', map.getZoom())
    },

    // ========== 样式控制方法 (Style Control Methods) ==========

    /**
     * 切换样式控制面板显示 (Toggle style control panel visibility)
     */
    toggleStyleControls() {
      this.showStyleControls = !this.showStyleControls
    },

    /**
     * 刷新当前样式 (Refresh current style)
     */
    async refreshCurrentStyle() {
      if (!this.$refs.worldMap || this.isRefreshing) return

      this.isRefreshing = true
      try {
        const success = await this.$refs.worldMap.refreshCurrentStyle()
        if (success) {
          console.log('样式刷新成功 (Style refresh successful)')
        } else {
          console.error('样式刷新失败 (Style refresh failed)')
        }
      } catch (error) {
        console.error('样式刷新错误 (Style refresh error):', error)
      } finally {
        this.isRefreshing = false
      }
    },

    /**
     * 强制刷新样式缓存 (Force refresh style cache)
     */
    async forceRefreshStyle() {
      if (!this.$refs.worldMap || this.isRefreshing) return

      this.isRefreshing = true
      try {
        const success = await this.$refs.worldMap.forceRefreshStyle()
        if (success) {
          console.log('强制样式刷新成功 (Force style refresh successful)')
        } else {
          console.error('强制样式刷新失败 (Force style refresh failed)')
        }
      } catch (error) {
        console.error('强制样式刷新错误 (Force style refresh error):', error)
      } finally {
        this.isRefreshing = false
      }
    },

    /**
     * 重新初始化地图 (Reinitialize map)
     */
    async reinitializeMap() {
      if (!this.$refs.worldMap || this.isRefreshing) return

      this.isRefreshing = true
      try {
        const success = await this.$refs.worldMap.restartMap()
        if (success) {
          console.log('地图重新初始化成功 (Map reinitialization successful)')
        } else {
          console.error('地图重新初始化失败 (Map reinitialization failed)')
        }
      } catch (error) {
        console.error('地图重新初始化错误 (Map reinitialization error):', error)
      } finally {
        this.isRefreshing = false
      }
    }
  },

  /**
   * 组件挂载后自动加载数据 (Auto load data after component mounted)
   */
  mounted() {
    console.log('云南-老挝主页组件已挂载 (Yunnan-Laos home component mounted)')

    // 自动加载云南-老挝路径数据 (Auto load Yunnan-Laos route data)
    this.loadYunnanLaosData()
  }
}
</script>

<style scoped>
/* 云南-老挝主页样式 - 全屏地图模式 (Yunnan-Laos Home Page Styles - Full Screen Map Mode) */
.yunnan-laos-home {
  width: 100vw;
  height: 100vh;
  background-color: rgba(15, 46, 44, 0.85);
  /* 半透明背景以显示背景图片 (Semi-transparent background to show background image) */
  position: relative;
  overflow: hidden;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  z-index: 2;
  /* 确保在背景图片之上但在地图组件之下 (Ensure above background image but below map component) */
}

/* 科技网格背景 (Tech Grid Background) */
.yunnan-laos-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  pointer-events: none;
  z-index: 5;
  /* 提高z-index以避免与背景图片冲突 (Increase z-index to avoid conflicts with background image) */
}

/* 样式控制面板 (Style Control Panel) */
.style-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(15, 46, 44, 0.9);
  border: 1px solid #1ED9B5;
  border-radius: 8px;
  padding: 15px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 150px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(30, 217, 181, 0.2);
}

.show-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

/* 控制按钮样式 (Control Button Styles) */
.control-btn {
  background: linear-gradient(135deg, #1ED9B5, #0F8A7A);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
}

.control-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0F8A7A, #1ED9B5);
  box-shadow: 0 2px 10px rgba(30, 217, 181, 0.4);
  transform: translateY(-1px);
}

.control-btn:disabled {
  background: rgba(76, 159, 123, 0.3);
  cursor: not-allowed;
  opacity: 0.6;
}

.control-btn.toggle-btn {
  background: linear-gradient(135deg, #FF6B6B, #FF5252);
}

.control-btn.toggle-btn:hover {
  background: linear-gradient(135deg, #FF5252, #FF6B6B);
}

.control-btn.show-btn {
  background: linear-gradient(135deg, #4CAF50, #45A049);
  padding: 10px 20px;
  font-size: 14px;
}

.control-btn.show-btn:hover {
  background: linear-gradient(135deg, #45A049, #4CAF50);
}
</style>
